[package]
name = "clara_fts"
version = "0.0.1"
license = "Apache-2.0"
edition = "2021"
publish = false

[dependencies]
anyhow = "1.0"
fastrand = "2.3.0"
lazy_static = "1.5.0"
memmap2 = "0.9.5"
once_cell = "1.20.2"
ordered-float = "5.0.0"
prost = "0.13"
stable_deref_trait = "1.2.0"
tantivy = "0.22.0"
tantivy-tokenizer-api = "0.3.0"
tempfile = "3.0"

[dependencies.charabia]
version = "0.9.6"
# Chinese normalization is disabled due to not useful, See https://github.com/meilisearch/charabia/issues/331
features = [
    "chinese-segmentation",
    "hebrew",
    "japanese",
    "thai",
    "korean",
    "greek",
    "khmer",
    "vietnamese",
    "swedish-recomposition",
    "turkish",
    "german-segmentation",
]
default-features = false

[build-dependencies]
prost-build = "0.13.4"

[dev-dependencies]
flate2 = "1.1.0"
paste = "1.0.15"
serde_json = "1.0.140"

[lints.clippy]
needless_lifetimes = "allow"
